{"main": {"id": "324e2b7a0a2aed88", "type": "split", "children": [{"id": "766ea47f9a261e33", "type": "tabs", "children": [{"id": "e81f88ccc9dbd3e6", "type": "leaf", "state": {"type": "thino_view", "state": {}, "icon": "Memos", "title": "<PERSON><PERSON>"}}, {"id": "b900236137e286c4", "type": "leaf", "state": {"type": "excalidraw", "state": {"file": "Excalidraw/重要与紧急.md"}, "icon": "excalidraw-icon", "title": "重要与紧急"}}], "currentTab": 1}], "direction": "vertical"}, "left": {"id": "9f3c145813b44c60", "type": "split", "children": [{"id": "f957f74ea5e4fa9a", "type": "tabs", "children": [{"id": "dd8c72fc438882f6", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "da0db952287911d9", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "6ed2f1edbbaa8377", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}, {"id": "783440871e4b4b21", "type": "leaf", "pinned": true, "state": {"type": "markdown", "state": {"file": "3-任务/Dashboard.md", "mode": "preview", "source": true}, "pinned": true, "icon": "lucide-file", "title": "Dashboard"}}]}], "direction": "horizontal", "width": 400}, "right": {"id": "128c55eacc93ffde", "type": "split", "children": [{"id": "a0ce31a991c18e01", "type": "tabs", "children": [{"id": "55ef978f5cc6ed6f", "type": "leaf", "state": {"type": "backlink", "state": {"file": "Excalidraw/重要与紧急.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "重要与紧急 的反向链接列表"}}, {"id": "fd7dcdd756b8fe14", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "Excalidraw/重要与紧急.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "重要与紧急 的出链列表"}}, {"id": "6fd3f438d60509c5", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "8078386359a20deb", "type": "leaf", "state": {"type": "outline", "state": {"file": "Excalidraw/重要与紧急.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "重要与紧急 的大纲"}}, {"id": "966bc8776aaa2e58", "type": "leaf", "state": {"type": "all-properties", "state": {"sortOrder": "frequency"}, "icon": "lucide-ghost", "title": "all-properties"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"float-search:Search obsidian in modal view": false, "obsidian-memos:Thino": false, "table-editor-obsidian:Advanced Tables Toolbar": false, "workspaces:管理工作区布局": false, "switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "templater-obsidian:Templater": false, "homepage:Open homepage": false, "obsidian-excalidraw-plugin:新建绘图文件": false}}, "active": "b900236137e286c4", "lastOpenFiles": ["3-任务/Dashboard.md", "复盘计划/模板-日度复盘.md", "2-输出/1-闪念笔记/闪念-2025-06-23.md", "3-任务/日记/日记-2024-01-04.md", "3-任务/日记/日记-2025-06-29.md", "2-输出/1-闪念笔记/闪念-2025-06-29.md", "复盘计划/2025-06-28-复盘记录-示例.md", "Excalidraw/重要与紧急.md", "复盘计划/快速参考-复盘要点清单.md", "复盘计划/操作指南-如何使用复盘系统.md", "复盘计划/README-复盘系统使用指南.md", "task/复盘模板-宏观市场分析.md", "task/01-目录结构设计.md", "task/任务分解-股票复盘系统-Markdown版-v1.0.md", "task/任务分解-个人股票交易复盘系统-v1.0.md", "prd/prd1.0.md", "复盘计划", "task", "prd", "6-可视化项目/存放测试的.html", "2-输出/5-AI-Diary/2025-06-28-用户思维特点与AI协作模式复盘.md", "5-数据分析/原始录入/原始识别-2025-06-27.md", "3-任务/日记/日记-2025-06-28.md", "2-输出/1-闪念笔记/闪念-2025-06-28.md", "5-数据分析/原始录入/原始识别-2025-06-27 copy.md", "3-任务/5-数据分析/1-数据管理/study_tracking.db", "5-数据分析/1-数据管理", "5-数据分析/原始录入/模板-原始识别.md", "5-数据分析/资金追踪记录.md", "5-数据分析/__pycache__/成交量对比工具.cpython-313.pyc", "5-数据分析/__pycache__", "3-任务/2025年第25周-MVP决策板.md", "3-任务/考研督导系统/markdown_storage/templates/quick_log_template.md", "5-数据分析/原始录入/原始识别-2025-06-26.md", "5-数据分析/初步分析", "check_data_inline.py", "0-辅助/Attachment/Pasted image 20250625115626.png", "0-辅助/Attachment/Pasted image 20250625115043.png", "0-辅助/Attachment/3c83a616e9f4c2d683adaff355b5df2.jpg", "0-辅助/Attachment/04fc54448b358ed570ebad49b6a25f4 1.png", "0-辅助/Attachment/Pasted image 20250625111237.png", "0-辅助/Attachment/Pasted image 20250625095925.png", "0-辅助/Attachment/04fc54448b358ed570ebad49b6a25f4.png", "0-辅助/Attachment/f95914d76bef4c5609144cd1704afbd.jpg", "0-辅助/Attachment/fc3fe12d8bedcde0de4959ba443ff90.png", "0-辅助/Attachment/Pasted image 20250614071834.png", "6-可视化项目/AI自动化的应用.canvas", "工作梳理.canvas", "未命名.canvas", "AI自动化的应用.canvas"]}